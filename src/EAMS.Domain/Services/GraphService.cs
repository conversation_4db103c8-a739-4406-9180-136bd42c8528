using EAMS.Domain.Interfaces;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace EAMS.Domain.Services;

public class GraphService: IGraphService
{
    private  readonly GraphServiceClient _graphClient;

    public GraphService(GraphServiceClient graphClient)
    {
        _graphClient = graphClient;
    }
    
    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphClient.Me.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Select = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id" };
        });    
        return graphUser;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        var searchResult = await _graphClient.Users.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Filter = $"mail eq 'email'";
            requestConfiguration.QueryParameters.Select = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id" };
        });

        if (searchResult.Value != null && searchResult.Value.Count > 0)
        {
            return searchResult.Value.First();
        }
        
        return null;
    }

    public Task<Invitation?> InviteUser(string email, string redirectUrl)
    {
        var invitation = new Invitation
        {
            InvitedUserEmailAddress = email,
            InviteRedirectUrl = redirectUrl,
            SendInvitationMessage = true,
            InvitedUserType = "Member"
        };

        return _graphClient.Invitations.PostAsync(invitation);
    }
}