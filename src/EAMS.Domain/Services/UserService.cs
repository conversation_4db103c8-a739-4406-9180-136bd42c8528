﻿using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Entities.User;

namespace EAMS.Domain.Services;

public class UserService : IUserService
{
    private readonly IGraphService _graphService;
    private readonly IUserRepository _userRepository;

    public UserService(IGraphService graphClient, IUserRepository userRepository)
    {
        _graphService = graphClient;  
        _userRepository = userRepository;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphService.GetCurrentLoginUserAsync();

        if (graphUser is not null && !string.IsNullOrEmpty(graphUser.Id))
        {
            var userId = Guid.Parse(graphUser.Id);
            var user = await _userRepository.GetByIdAsync(userId);

            if (user is not null)
            {
                user.GraphUser = graphUser;
                return user;
            }
            else
            {
                // create a new user in the database.
                var newUser = new User
                {
                    Id = userId,
                };
                
                await _userRepository.AddAsync(newUser);
                newUser.GraphUser = graphUser;
                return newUser;
            }
        }
        
        return null;
    }

    public async Task<Invitation?> CreateInvitationAsync(UserInvitation newInvitation)
    {
        throw new NotImplementedException();
    }
}
