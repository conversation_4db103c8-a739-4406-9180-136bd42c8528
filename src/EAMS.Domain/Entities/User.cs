using System.ComponentModel.DataAnnotations.Schema;
using GraphUser = Microsoft.Graph.Models.User;
namespace EAMS.Domain.Entities;

public class User: SoftDeletableEntity<Guid>
{
    public Guid? OrganisationId { get; set; }
    public string? Position { get; set; }
    
    [NotMapped]
    public GraphUser GraphUser { get; set; }
    
    // Navigation properties for relationships
    public Organisation Organisation { get; set; }
    public ICollection<UserInvitation> InvitationsSent { get; set; } = new List<UserInvitation>();
    public ICollection<UserInvitation> InvitationsReceived { get; set; } = new List<UserInvitation>();

}