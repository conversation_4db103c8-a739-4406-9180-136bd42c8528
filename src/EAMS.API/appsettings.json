{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "qualified.domain.name", "TenantId": "22222222-2222-2222-2222-222222222222", "ClientId": "11111111-1111-1111-11111111111111111", "ClientSecret": "11111111-1111-1111-11111111111111111111", "Scopes": {"AccommodationsRead": ["Accommodations.Read"], "AccommodationsWrite": ["Accommodations.ReadWrite"], "Organisations": ["Organisations"]}}, "SwaggerOAuth": {"ClientId": "33333333-3333-3333-3333-333333333333", "Scopes": "api://11111111-1111-1111-1111-111111111111/access_as_user"}, "GraphApi": {"Scopes": ["User.Read", "User.ReadBasic.All"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "", "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=00000000-0000-0000-0000-000000000000;IngestionEndpoint=https://xxxx.applicationinsights.azure.com/;"}}