using EAMS.Domain.Interfaces;
using EAMS.Domain.Services;
using EAMS.Infrastructure.Repositories;
using Microsoft.OpenApi.Models;

namespace EAMS.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddRepositoryServices(this IServiceCollection services)
        {
            // services.AddScoped(typeof(IRepository<,>), typeof(Repository<,>));
            services.AddScoped<IAccommodationRepository, AccommodationRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IOrganisationRepository, OrganisationRepository>();
            services.AddScoped<IAmenityRepository, AmenityRepository>();
            services.AddScoped<IAmenityOptionsRepository, AmenityOptionsRepository>();

            return services;
        }

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddScoped<IAccommodationService, AccommodationService>();
            services.AddScoped<IOrganisationService, OrganisationService>();
            services.AddScoped<IAmenityService, AmenityService>();
            services.AddScoped<IAmenityOptionsService, AmenityOptionsService>();
            services.AddScoped<IGraphService, GraphService>();
            services.AddScoped<IUserService, UserService>();
            
            return services;
        }

        public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new() { Title = "EAMS API", Version = "v1" });

                // Custom schema ID generator to handle Microsoft Graph type conflicts
                c.CustomSchemaIds(type =>
                {
                    // Handle Microsoft Graph Group conflicts
                    if (type.FullName == "Microsoft.Graph.Models.Group")
                        return "GraphGroup";
                    if (type.FullName == "Microsoft.Graph.Models.TermStore.Group")
                        return "TermStoreGroup";

                    // For other types, use the default behavior
                    return type.Name;
                });

                // OAuth2 authorization
                var tenantId = configuration["AzureAd:TenantId"];
                var authUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/authorize");
                var tokenUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token");
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = authUrl,
                            TokenUrl = tokenUrl,
                            Scopes = new Dictionary<string, string>
                            {
                                {configuration["SwaggerOAuth:Scopes"]!, "Access as user"}
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        new[] { configuration["SwaggerOAuth:Scopes"]! }
                    }
                });
            });

            return services;
        }
    }
}